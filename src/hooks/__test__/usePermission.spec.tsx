import React from 'react';
import { renderHook } from '@testing-library/react';
import { vi } from 'vitest';

import usePermission from '+hooks/usePermission';
import { Storage } from '+services/storage-services';
import { sessionKeys } from '+utils';

const { useLocation } = vi.hoisted(() => {
  return { useLocation: vi.fn() };
});
vi.mock('react-router-dom', async () => {
  const actualReactRouter = await vi.importActual('react-router-dom');
  return {
    ...actualReactRouter,
    useLocation
  };
});
const MockedHome = () => <div>Home</div>;
const MockedMerchant = () => <div>Merchant</div>;
const MockedMerchantDetail = () => <div>Merchant Detail</div>;
const MockedPayIns = () => <div>PayIns</div>;
const MockedPayouts = () => <div>Payouts</div>;
const MockedReversals = () => <div>Reversals</div>;
const MockedVirtualAccounts = () => <div>Virtual Accounts</div>;
const MockedWebhook = () => <div>Webhooks</div>;
const MockedSettlement = () => <div>Settlement</div>;
const MockedSettlementPayout = () => <div>Settlement Payout</div>;
const MockedSettings = () => <div>Settings</div>;
const MockedAuditLog = () => <div>Audit Log</div>;
const MockedMerchantKYC = () => <div>Merchant KYC</div>;

const dummyToken =
  '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

const mockedDashboardComponent = {
  dashboard: ['home', 'os-icon-home', MockedHome],
  merchants: ['merchants', 'os-icon-users', MockedMerchant],
  virtual_accounts: ['virtual-accounts', 'os-icon-hash', MockedVirtualAccounts],
  pay_ins: ['pay-ins', 'os-icon-arrow-down-left', MockedPayIns],
  payouts: ['payouts', 'os-icon-arrow-up-right', MockedPayouts],
  payment_reversals: ['reversals', 'os-icon-rotate-ccw', MockedReversals],
  settlement: ['settlements', 'os-icon-zap', MockedSettlement],
  settlement_payouts: ['settlementPayouts', 'os-icon-navigation', MockedSettlementPayout],
  webhooks: ['webHooks', 'os-icon-git-pull-request', MockedWebhook],
  audit_logs: ['audit-logs', 'os-icon-clipboard', MockedAuditLog],
  default_fee_config: ['settings', 'os-icon-ui-46', MockedSettings],
  merchant_general_details: ['', null, MockedMerchantDetail],
  merchant_kyc: ['', null, MockedMerchantKYC]
};

const mockedDashboardComponents = {
  merchants: ['merchants', 'os-icon-users', MockedMerchant],
  virtual_accounts: ['virtual-accounts', 'os-icon-hash', MockedVirtualAccounts],
  settlement: ['settlements', 'os-icon-zap', MockedSettlement],
  webhooks: ['webHooks', 'os-icon-git-pull-request', MockedWebhook],
  audit_logs: ['audit-logs', 'os-icon-clipboard', MockedAuditLog],
  default_fee_config: ['settings', 'os-icon-ui-46', MockedSettings]
};
beforeEach(() => {
  Storage.setItem(sessionKeys.ADMIN_USER_TOKEN, dummyToken);
  useLocation.mockImplementation(() => ({ pathname: '' }));
});
describe('Permission Hook', () => {
  it('Should return component with permission available in mocked Permission', () => {
    const { result } = renderHook(() => usePermission(mockedDashboardComponent));
    expect(result.current[1]).toHaveLength(24);
  });

  it('Should return component with the permission available in mockedDashboardComponents', () => {
    const { result } = renderHook(() => usePermission(mockedDashboardComponents));
    expect(result.current[1]).toHaveLength(18);
  });
});
