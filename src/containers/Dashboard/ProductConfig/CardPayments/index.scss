@import 'styles/kpy-custom/variables';

.card-payments-content {
  display: flex;
  flex-direction: column;
  margin-top: 2rem;
  font-family: 'Averta PE';

  & > * + * {
    margin-top: 30px;
  }

  .first-section {
    align-items: flex-start;
    display: flex;
    justify-content: space-between;

    .title-wrapper {
      gap: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      max-width: 60%;

      .title-with-badge {
        display: flex;
        align-items: center;
        column-gap: 1rem;

        .status {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80px;
          font-size: 14px;
          font-weight: 500;
          height: 24px;
          border-radius: 5px;
        }
      }

      .title {
        font-weight: 600;
        color: #414f5f;
        font-size: 24px;
        margin-bottom: 0.5rem;
      }

      .subtitle {
        font-weight: 500;
        color: #a9afbc;
        font-size: 14px;
        margin-bottom: 0.5rem;
        line-height: 20px;
        max-width: 30rem;
      }
    }

    .controls {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 12px;

      .--disable-btn {
        background-color: white;
        color: #2376f3;
        border: 1px solid #dde2ec;
        font-weight: 600;
        padding: 8px 16px;
        border-radius: 5px;
        font-size: 14px;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f9fbfd;
          border-color: #2376f3;
        }
      }

      .custom-config-alert {
        display: flex;
        align-items: center;
        gap: 8px;
        background-color: #fff8e1;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 14px;
        color: #fa9500;
        font-weight: 500;

        .os-icon-alert-triangle {
          color: #fa9500;
          font-size: 16px;
        }

        .export-btn {
          color: #2376f3;
          font-weight: 600;
          font-size: 14px;
          padding: 0 8px;
          text-decoration: none;
          background: none;
          border: none;
          display: flex;
          align-items: center;
          gap: 4px;

          &:hover {
            text-decoration: underline;
          }

          .os-icon-download {
            font-size: 16px;
          }
        }
      }
    }
  }

  .table-section {
    .os-tabs-w {
      margin-bottom: 20px;

      .nav-tabs {
        border-bottom: 2px solid #f1f6fa;

        .nav-item {
          cursor: pointer;
          
          .nav-link {
            color: #a9afbc;
            font-weight: 500;
            font-size: 16px;
            padding: 12px 20px;
            border: none;
            background: none;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;

            &.active {
              color: #2376f3;
              border-bottom-color: #2376f3;
              font-weight: 600;
            }

            &:hover:not(.active) {
              color: #414f5f;
            }
          }
        }
      }
    }

    .merchants-summary {
      margin-bottom: 20px;
      
      p {
        color: #414f5f;
        font-size: 16px;
        font-weight: 400;
        margin: 0;
      }
    }
  }
}

// Table-specific styles
.--card-payments-table {
  &.--heading,
  &.--row {
    div {
      @media (min-width: 1000px) {
        // Checkbox column
        &:nth-child(1) {
          width: 5%;
          min-width: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        // Merchant name column
        &:nth-child(2) {
          width: 30%;
          display: flex;
          align-items: center;
          font-weight: 500;
          color: #414f5f;
          font-size: 18px;
        }

        // Risk Level column
        &:nth-child(3) {
          width: 15%;
          display: flex;
          align-items: center;
        }

        // Status column
        &:nth-child(4) {
          width: 15%;
          display: flex;
          align-items: center;
          gap: 8px;

          .status-pill {
            width: 9px;
            height: 9px;
            border-radius: 50%;
            
            &.enabled {
              background-color: #24b314;
            }
            
            &.disabled {
              background-color: #a9afbc;
            }
          }
        }

        // Configuration Type column
        &:nth-child(5) {
          width: 20%;
          display: flex;
          align-items: center;

          .custom-config {
            color: #414f5f;
            font-weight: 500;
            font-style: italic;
          }

          .lighter {
            color: #94a7b7;
            font-weight: 400;
          }
        }

        // Date Added column
        &:nth-child(6) {
          width: 15%;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: center;
          text-align: right;

          .date-text {
            font-weight: 600;
            color: #414f5f;
            font-size: 18px;
          }

          .time-text {
            color: #a9afbc;
            font-size: 18px;
            margin-left: 0;
            margin-top: 2px;
          }
        }
      }

      // Mobile responsive
      @media (max-width: 999px) {
        div {
          width: 100% !important;
          min-height: 44px;
          display: flex;
          align-items: center;
          border-bottom: 1px solid #f1f6fa;
          padding: 8px 0;

          .body-row-header {
            font-weight: 600;
            color: #414f5f;
            min-width: 120px;
            margin-right: 10px;
          }
        }
      }
    }
  }

  // Row highlighting for custom configurations
  &.--row[style*="background-color: rgb(255, 247, 237)"] {
    background-color: #fff7ed !important;
    
    &:hover {
      background-color: #fef3e7 !important;
    }
  }

  // Risk indicator integration
  .risk-label {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;

    &.--low {
      background-color: #e4fff1;
      color: #24b314;
    }

    &.--mid {
      background-color: #fff8e1;
      color: #fa9500;
    }

    &.--high {
      background-color: #ffd2da;
      color: #f32345;
    }

    img {
      width: 12px;
      height: 12px;
      object-fit: contain;
    }
  }
}

// Filter section styling
.filter-section {
  background-color: #f9fbfd;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;

  .filter-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;

    .search-input {
      flex: 1;
      min-width: 250px;
      
      input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #dde2ec;
        border-radius: 5px;
        font-size: 16px;
        color: #414f5f;
        
        &::placeholder {
          color: #a9afbc;
        }
        
        &:focus {
          outline: none;
          border-color: #2376f3;
        }
      }
    }

    .filter-dropdown {
      min-width: 150px;
      
      select {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #dde2ec;
        border-radius: 5px;
        font-size: 16px;
        color: #414f5f;
        background-color: white;
        
        &:focus {
          outline: none;
          border-color: #2376f3;
        }
      }
    }

    .date-filter {
      display: flex;
      align-items: center;
      gap: 8px;

      input[type="date"] {
        padding: 12px 16px;
        border: 2px solid #dde2ec;
        border-radius: 5px;
        font-size: 16px;
        color: #414f5f;
        
        &:focus {
          outline: none;
          border-color: #2376f3;
        }
      }
    }

    .go-button {
      background-color: #3e4b5b;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #2a3441;
      }

      .os-icon {
        margin-left: 4px;
      }
    }
  }
}

// Pagination and table footer styling
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-top: 2px solid #f1f6fa;
  margin-top: 20px;

  .pagination-info {
    display: flex;
    align-items: center;
    gap: 20px;

    .items-per-page {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #414f5f;
      font-size: 16px;

      select {
        padding: 8px 12px;
        border: 2px solid #dde2ec;
        border-radius: 5px;
        background-color: #dde2ec;
        font-weight: 600;
        color: #414f5f;
      }
    }

    .showing-text {
      color: #414f5f;
      font-size: 16px;

      .bold {
        font-weight: 600;
      }
    }
  }

  .back-to-top {
    background-color: #f9fbfd;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    box-shadow: 0px 5px 7px rgba(126, 142, 177, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    color: #414f5f;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #eaf2fe;
    }

    .os-icon {
      font-size: 16px;
    }
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;

    .page-btn {
      width: 40px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.2s ease;

      &.prev, &.next {
        background-color: #f1f6fa;
        color: #a9afbc;
        
        &:hover:not(:disabled) {
          background-color: #dde2ec;
        }
        
        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }

      &.current {
        background-color: #2376f3;
        color: white;
        font-weight: 600;
      }
    }

    .go-to-page {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: 16px;

      input {
        width: 44px;
        height: 45px;
        text-align: center;
        border: 2px solid #a9afbc;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 600;
        color: #a9afbc;
        
        &:focus {
          outline: none;
          border-color: #2376f3;
          color: #414f5f;
        }
      }

      .go-btn {
        background-color: #a9afbc;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 5px;
        font-weight: 600;
        cursor: pointer;
        
        &:hover {
          background-color: #94a7b7;
        }
      }
    }
  }
}
