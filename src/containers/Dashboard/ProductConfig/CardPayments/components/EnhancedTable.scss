@import 'styles/kpy-custom/variables';

.enhanced-table-container {
  background-color: #ffffff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .table-wrapper {
    width: 100%;
  }

  // Table Header
  .table-header {
    display: flex;
    align-items: center;
    background-color: #f9fbfd;
    padding: 15px 24px;
    border-bottom: 1px solid #f1f6fa;
    font-family: 'Averta PE', sans-serif;
    
    .header-cell {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #292b2c;
      opacity: 0.4;

      &.checkbox-cell {
        width: 40px;
        justify-content: center;
        padding-right: 16px;
      }

      &.merchant-cell {
        flex: 1;
        min-width: 200px;
      }

      &.risk-cell {
        width: 120px;
        justify-content: center;
      }

      &.status-cell {
        width: 100px;
        justify-content: center;
      }

      &.config-cell {
        width: 180px;
      }

      &.date-cell {
        width: 150px;
        justify-content: flex-end;
      }
    }

    .table-checkbox {
      width: 20px;
      height: 20px;
      border: 2px solid #a9afbc;
      border-radius: 3px;
      cursor: pointer;
      
      &:checked {
        background-color: #2376f3;
        border-color: #2376f3;
      }
    }
  }

  // Table Body
  .table-body {
    .table-row {
      display: flex;
      align-items: center;
      padding: 19px 24px;
      border-bottom: 2px solid #f1f6fa;
      cursor: pointer;
      transition: background-color 0.2s ease;
      min-height: 64px;

      &:hover {
        background-color: #f9fbfd;
      }

      &.custom-config-row {
        background-color: #fff7ed;

        &:hover {
          background-color: #fef3e7;
        }
      }

      .body-cell {
        display: flex;
        align-items: center;
        font-family: 'Averta PE', sans-serif;
        font-size: 18px;
        line-height: 26px;

        &.checkbox-cell {
          width: 40px;
          justify-content: center;
          padding-right: 16px;
        }

        &.merchant-cell {
          flex: 1;
          min-width: 200px;

          .merchant-name {
            color: #414f5f;
            font-weight: 400;
          }
        }

        &.risk-cell {
          width: 120px;
          justify-content: center;

          .risk-badge {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 700;

            &.risk-low {
              background-color: #e4fff1;
              color: #24b314;

              .risk-icon {
                transform: rotate(180deg);
                color: #24b314;
              }
            }

            &.risk-mid {
              background-color: #fff8e1;
              color: #fa9500;

              .minus {
                font-weight: 900;
                font-size: 14px;
              }
            }

            &.risk-high {
              background-color: #ffd2da;
              color: #f32345;

              .arrow-up {
                color: #f32345;
              }
            }

            .risk-icon {
              width: 16px;
              height: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              font-weight: 700;
            }
          }
        }

        &.status-cell {
          width: 100px;
          justify-content: flex-start;

          .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;

            .status-dot {
              width: 9px;
              height: 9px;
              border-radius: 50%;

              &.enabled {
                background-color: #24b314;
              }

              &.disabled {
                background-color: #a9afbc;
              }
            }

            .status-text {
              color: #414f5f;
              font-weight: 400;
            }
          }
        }

        &.config-cell {
          width: 180px;

          .config-type {
            &.default {
              color: #94a7b7;
              font-weight: 400;
            }

            &.custom {
              color: #414f5f;
              font-weight: 400;
              font-style: italic;
            }
          }
        }

        &.date-cell {
          width: 150px;
          justify-content: flex-end;
          flex-direction: column;
          align-items: flex-end;

          .date-container {
            text-align: right;

            .date-main {
              display: block;
              color: #414f5f;
              font-weight: 600;
              font-size: 18px;
              line-height: 26px;
            }

            .date-time {
              display: block;
              color: #a9afbc;
              font-weight: 400;
              font-size: 18px;
              line-height: 26px;
            }
          }
        }
      }
    }
  }

  // Table Footer
  .table-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-top: 2px solid #f1f6fa;
    font-family: 'Averta PE', sans-serif;
    background-color: #ffffff;

    .footer-left {
      display: flex;
      align-items: center;
      gap: 30px;

      .items-control {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #414f5f;
        font-size: 18px;
        font-weight: 400;

        .show-text,
        .items-text {
          color: #414f5f;
        }

        .items-select {
          height: 46px;
          padding: 8px 12px;
          border: none;
          border-radius: 5px;
          background-color: #dde2ec;
          color: #414f5f;
          font-family: 'Averta PE', sans-serif;
          font-size: 18px;
          font-weight: 700;
          cursor: pointer;
          appearance: none;
          background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23414f5f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
          background-repeat: no-repeat;
          background-position: right 8px center;
          background-size: 16px;
          padding-right: 32px;
        }
      }

      .divider {
        width: 2px;
        height: 20px;
        background-color: #dde2ec;
        transform: rotate(90deg);
      }

      .showing-text {
        color: #414f5f;
        font-size: 18px;
        font-weight: 400;

        strong {
          font-weight: 700;
        }
      }
    }

    .footer-center {
      .back-to-top-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 20px;
        background-color: #f9fbfd;
        border: none;
        border-radius: 20px;
        box-shadow: 0px 5px 7px rgba(126, 142, 177, 0.1);
        color: #414f5f;
        font-family: 'Averta PE', sans-serif;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #eaf2fe;
        }

        .arrow-up-icon {
          font-size: 20px;
        }
      }
    }

    .footer-right {
      display: flex;
      align-items: center;
      gap: 20px;

      .pagination-controls {
        display: flex;
        align-items: center;
        gap: 7px;

        .page-btn {
          width: 33px;
          height: 45px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          transition: all 0.2s ease;
          color: #a9afbc;
          font-size: 18px;

          &.prev-btn,
          &.next-btn {
            background-color: #f1f6fa;

            &:hover:not(:disabled) {
              background-color: #dde2ec;
            }

            &:disabled {
              cursor: not-allowed;
              opacity: 0.5;
            }
          }
        }

        .current-page {
          background-color: #2376f3;
          color: #ffffff;
          font-weight: 600;
          font-size: 18px;
          line-height: 26px;
          padding: 8px 12px;
          border-radius: 5px;
          min-width: 40px;
          text-align: center;
        }
      }

      .go-to-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        .go-to-text {
          color: #a9afbc;
          font-family: 'Averta PE', sans-serif;
          font-size: 18px;
          font-weight: 700;
        }

        .page-input {
          width: 44px;
          height: 45px;
          text-align: center;
          border: 2px solid #a9afbc;
          border-radius: 5px;
          font-family: 'Averta PE', sans-serif;
          font-size: 18px;
          font-weight: 700;
          color: #a9afbc;
          background-color: #ffffff;

          &:focus {
            outline: none;
            border-color: #2376f3;
            color: #414f5f;
          }
        }

        .go-btn {
          background-color: #a9afbc;
          color: #ffffff;
          border: none;
          padding: 8px 16px;
          border-radius: 5px;
          font-family: 'Averta PE', sans-serif;
          font-size: 18px;
          font-weight: 700;
          cursor: pointer;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: #94a7b7;
          }
        }
      }
    }
  }

  // Loading State
  .enhanced-table-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    
    .loading-spinner {
      color: #414f5f;
      font-size: 18px;
    }
  }

  // Mobile Responsive
  @media (max-width: 1024px) {
    .table-header,
    .table-body .table-row {
      flex-direction: column;
      align-items: stretch;

      .header-cell,
      .body-cell {
        width: 100% !important;
        justify-content: flex-start !important;
        padding: 8px 0;
        border-bottom: 1px solid #f1f6fa;

        &.checkbox-cell {
          justify-content: center !important;
        }

        &.date-cell {
          flex-direction: row !important;
          align-items: center !important;
          justify-content: space-between !important;

          .date-container {
            text-align: left !important;
            display: flex;
            gap: 10px;
          }
        }
      }
    }

    .table-footer {
      flex-direction: column;
      gap: 20px;

      .footer-left,
      .footer-right {
        flex-direction: column;
        gap: 15px;
      }

      .footer-center {
        order: -1;
      }
    }
  }
}
