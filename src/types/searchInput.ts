import { FilterMerchantType, IInput } from './identity';

export interface ISearchInput extends IInput {
  dropdownData?: FilterMerchantType[];
  setDebouncedSearchInput?: (e: string) => void;
  setShouldRunQuery?: (e: boolean) => void;
  isFetching: boolean;
  disabled?: boolean;
  wrapperClassName?: string;
  onBlur?: React.EventHandler<React.FocusEvent>;
  accessorKey?: keyof FilterMerchantType | null;
  onChange: (e: string | string[] | number | FilterMerchantType) => void;
  name: string;
}
