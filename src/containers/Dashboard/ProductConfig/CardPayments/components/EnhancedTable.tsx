import { useSearchQuery } from '+hooks';
import React, { useState } from 'react';

import './EnhancedTable.scss';

interface IEnhancedTableProps {
  data: any[];
  loading: boolean;
  totalItems: number;
  pageSize: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  onRowClick?: (item: any) => void;
}

const EnhancedTable: React.FC<IEnhancedTableProps> = ({
  data,
  loading,
  totalItems,
  pageSize,
  currentPage,
  onPageChange,
  onLimitChange,
  onRowClick
}) => {
  const searchQuery = useSearchQuery();
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [allSelected, setAllSelected] = useState(false);

  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);

  const handleSelectAll = (checked: boolean) => {
    setAllSelected(checked);
    if (checked) {
      setSelectedRows(data.map(item => item.id));
    } else {
      setSelectedRows([]);
    }
  };

  const handleRowSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedRows([...selectedRows, id]);
    } else {
      setSelectedRows(selectedRows.filter(rowId => rowId !== id));
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (loading) {
    return (
      <div className="enhanced-table-loading">
        <div className="loading-spinner">Loading...</div>
      </div>
    );
  }

  return (
    <div className="enhanced-table-container">
      <div className="table-wrapper">
        {/* Table Header */}
        <div className="table-header">
          <div className="header-cell checkbox-cell">
            <input
              type="checkbox"
              checked={allSelected}
              onChange={(e) => handleSelectAll(e.target.checked)}
              className="table-checkbox"
            />
          </div>
          <div className="header-cell merchant-cell">Merchant</div>
          <div className="header-cell risk-cell">Risk Level</div>
          <div className="header-cell status-cell">Status</div>
          <div className="header-cell config-cell">Configuration Type</div>
          <div className="header-cell date-cell">Date added</div>
        </div>

        {/* Table Body */}
        <div className="table-body">
          {data.map((item, index) => {
            const isCustomConfig = item.configuration_type === 'custom';
            const isSelected = selectedRows.includes(item.id);
            
            return (
              <div 
                key={item.id} 
                className={`table-row ${isCustomConfig ? 'custom-config-row' : ''}`}
                onClick={() => onRowClick?.(item)}
              >
                <div className="body-cell checkbox-cell">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={(e) => handleRowSelect(item.id, e.target.checked)}
                    className="table-checkbox"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
                <div className="body-cell merchant-cell">
                  <span className="merchant-name">{item.name}</span>
                </div>
                <div className="body-cell risk-cell">
                  <div className={`risk-badge risk-${item.risk_level}`}>
                    <div className="risk-icon">
                      {item.risk_level === 'low' && <span className="arrow-down">↓</span>}
                      {item.risk_level === 'mid' && <span className="minus">-</span>}
                      {item.risk_level === 'high' && <span className="arrow-up">↑</span>}
                    </div>
                    <span className="risk-text">{item.risk_level.toUpperCase()}</span>
                  </div>
                </div>
                <div className="body-cell status-cell">
                  <div className="status-indicator">
                    <span className={`status-dot ${item.status}`}></span>
                    <span className="status-text">{item.status === 'enabled' ? 'Enabled' : 'Disabled'}</span>
                  </div>
                </div>
                <div className="body-cell config-cell">
                  <span className={`config-type ${isCustomConfig ? 'custom' : 'default'}`}>
                    {item.configuration_type === 'custom' ? 'Custom' : 'Default'}
                  </span>
                </div>
                <div className="body-cell date-cell">
                  <div className="date-container">
                    <span className="date-main">24 Jul 2023</span>
                    <span className="date-time">12:57 PM</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Table Footer */}
        <div className="table-footer">
          <div className="footer-left">
            <div className="items-control">
              <span className="show-text">Show</span>
              <select
                value={pageSize}
                onChange={(e) => onLimitChange(Number(e.target.value))}
                className="items-select"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span className="items-text">items</span>
            </div>
            <div className="divider"></div>
            <div className="showing-text">
              Showing <strong>{startItem}</strong> to <strong>{endItem}</strong> of <strong>{totalItems}</strong> items.
            </div>
          </div>

          <div className="footer-center">
            <button className="back-to-top-btn" onClick={scrollToTop}>
              <span>Back to top</span>
              <i className="arrow-up-icon">↑</i>
            </button>
          </div>

          <div className="footer-right">
            <div className="pagination-controls">
              <button
                className="page-btn prev-btn"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <i className="arrow-left">←</i>
              </button>
              
              <div className="current-page">{currentPage}</div>
              
              <button
                className="page-btn next-btn"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={endItem >= totalItems}
              >
                <i className="arrow-right">→</i>
              </button>
            </div>

            <div className="go-to-controls">
              <span className="go-to-text">Go to:</span>
              <input
                type="number"
                min="1"
                max={Math.ceil(totalItems / pageSize)}
                defaultValue={currentPage}
                className="page-input"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    const page = Number((e.target as HTMLInputElement).value);
                    if (page >= 1 && page <= Math.ceil(totalItems / pageSize)) {
                      onPageChange(page);
                    }
                  }
                }}
              />
              <button
                className="go-btn"
                onClick={() => {
                  const input = document.querySelector('.page-input') as HTMLInputElement;
                  const page = Number(input.value);
                  if (page >= 1 && page <= Math.ceil(totalItems / pageSize)) {
                    onPageChange(page);
                  }
                }}
              >
                Go
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedTable;
