import React from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

import Badge from '+containers/Dashboard/Shared/Badge';
import Typography from '+containers/Dashboard/Shared/Typography';
import { useFeedbackHandler, useSearchQuery, useSetUserAccess } from '+hooks';
import APIRequest from '+services/api-services';
import { CurrencyType, RiskLevelType } from '+types';
import {
  capitalize,
  capitalizeRemovedash,
  filteredOutObjectProperty,
  formatProductConfigStatus,
  getDate,
  getTime,
  history,
  isAllowed,
  queriesParams,
  switchStatus
} from '+utils';

import Table from '../../Shared/Table';
import RiskIndicator from '../components/RiskIndicator';

import './index.scss';

interface ICardPaymentMerchant {
  id: string;
  name: string;
  email: string;
  risk_level: RiskLevelType;
  status: string;
  configuration_type: 'default' | 'custom';
  created_at: string;
}

interface ICardPaymentConfig {
  enabled: boolean;
  total_merchants: number;
  merchants_with_custom_config: number;
}

const TabInfo = {
  default: 'Default Configuration',
  access: 'Merchants with Access',
  requesting: 'Merchants Requesting Access'
};

const api = new APIRequest();

const CardPayments = () => {
  const { currency = 'USD' } = useParams<{ currency: CurrencyType }>();
  const userAccess = useSetUserAccess();
  const { feedbackInit } = useFeedbackHandler();
  const searchQuery = useSearchQuery();

  const activeTab = searchQuery.value.tab || 'access';
  const page = searchQuery.value.page || '1';
  const limit = searchQuery.value.limit || '50';
  const keyword = searchQuery.value.keyword || undefined;
  const selectedConfig = searchQuery.value.selectedConfig || undefined;
  const status = searchQuery.value.status || undefined;
  const dateFrom = searchQuery.value.dateFrom || undefined;
  const dateTo = searchQuery.value.dateTo || undefined;

  const sortingParams = {
    limit,
    page,
    type: selectedConfig,
    merchant_name: keyword,
    status: typeof status === 'string' ? [status] : status,
    date_from: dateFrom,
    date_to: dateTo,
    ...filteredOutObjectProperty(searchQuery.value, [
      queriesParams.tab,
      queriesParams.page,
      queriesParams.limit,
      queriesParams.dateFrom,
      queriesParams.dateTo,
      queriesParams.status,
      queriesParams.keyword,
      queriesParams.selectedConfig
    ])
  };

  // Mock data for now - replace with actual API calls
  const mockData: ICardPaymentMerchant[] = [
    {
      id: '1',
      name: 'Glamorous Ltd',
      email: '<EMAIL>',
      risk_level: 'low',
      status: 'enabled',
      configuration_type: 'default',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '2',
      name: 'Redlight Motor Limited',
      email: '<EMAIL>',
      risk_level: 'low',
      status: 'enabled',
      configuration_type: 'default',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '3',
      name: 'Lixar Business Solutions',
      email: '<EMAIL>',
      risk_level: 'mid',
      status: 'enabled',
      configuration_type: 'default',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '4',
      name: 'Lambda Technologies',
      email: '<EMAIL>',
      risk_level: 'low',
      status: 'enabled',
      configuration_type: 'custom',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '5',
      name: 'Smart Company',
      email: '<EMAIL>',
      risk_level: 'high',
      status: 'disabled',
      configuration_type: 'custom',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '6',
      name: 'Renowned Christian Center',
      email: '<EMAIL>',
      risk_level: 'low',
      status: 'disabled',
      configuration_type: 'default',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '7',
      name: 'Primex',
      email: '<EMAIL>',
      risk_level: 'high',
      status: 'enabled',
      configuration_type: 'default',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '8',
      name: 'Mamboo Corp.',
      email: '<EMAIL>',
      risk_level: 'low',
      status: 'enabled',
      configuration_type: 'custom',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '9',
      name: 'Expede',
      email: '<EMAIL>',
      risk_level: 'low',
      status: 'enabled',
      configuration_type: 'default',
      created_at: '2023-07-24T12:57:00Z'
    },
    {
      id: '10',
      name: 'Levitate.io',
      email: '<EMAIL>',
      risk_level: 'low',
      status: 'enabled',
      configuration_type: 'custom',
      created_at: '2023-07-24T12:57:00Z'
    }
  ];

  const mockConfig: ICardPaymentConfig = {
    enabled: true,
    total_merchants: 15,
    merchants_with_custom_config: 9
  };

  // Mock query hooks
  const { data, refetch, isLoading } = useQuery(
    [`CARD_PAYMENTS_${currency}_MERCHANTS`, limit, page, sortingParams],
    () => Promise.resolve({ data: mockData, paging: { page_size: 50, total_items: 15, current_page: 1 } }),
    {
      refetchOnMount: 'always'
    }
  );

  const { data: configData } = useQuery(
    [`CARD_PAYMENTS_${currency}_CONFIG`, currency],
    () => Promise.resolve({ data: { setting: mockConfig } }),
    {
      refetchOnMount: 'always'
    }
  );

  const currencyStatus = configData?.data?.setting?.enabled;
  const totalMerchants = configData?.data?.setting?.total_merchants || 0;
  const customConfigMerchants = configData?.data?.setting?.merchants_with_custom_config || 0;

  const merchantsCardPayments = {
    className: '--card-payments-table',
    emptyStateHeading: 'No Merchants yet',
    data: data?.data || [],
    emptyStateMessage: 'There are no Merchants with Card Payment access',
    rowURL: `/dashboard/product-config/card-payments/${currency}/merchants`,
    rowKey: 'id',
    fields: (each: ICardPaymentMerchant) => ({
      data: {
        Merchant: <span id="merchant-name">{capitalize(each.name)}</span>,
        Risk_Level: <RiskIndicator riskLevel={each.risk_level as RiskLevelType} />,
        Status: (
          <>
            <span className={`status-pill smaller ${switchStatus(formatProductConfigStatus(each.status))}`} />
            <span>{capitalizeRemovedash(formatProductConfigStatus(each.status))}</span>
          </>
        ),
        Configuration_Type: (
          <span className={each.configuration_type === 'custom' ? 'custom-config' : 'lighter'}>
            {capitalize(each.configuration_type)}
          </span>
        ),
        Date_Added: (
          <>
            <span className="date-text">{getDate(each.created_at)}</span>
            <span className="annotation time-text">{getTime(each.created_at)}</span>
          </>
        )
      },
      style: each.configuration_type === 'custom' ? { backgroundColor: '#FFF7ED' } : undefined
    })
  };

  return (
    <div className="content-i">
      <div className="content-box">
        <div className="row">
          <button type="button" className="btn btn-link mb-2" onClick={() => history.push('/dashboard/product-config')}>
            <i className="os-icon os-icon-arrow-left7" />
            <span className="font-weight-bold">Back to USD Products</span>
          </button>
        </div>
        <div className="content card-payments-content">
          <div className="first-section">
            <div className="title-wrapper">
              <span className="title-with-badge">
                <Typography variant="h2">Card Payments [USD]</Typography>
                {!isLoading && <Badge status={currencyStatus ? 'enabled' : 'disabled'}>{currencyStatus ? 'Enabled' : 'Disabled'}</Badge>}
              </span>
              <Typography variant="subtitle2">
                Here, you'll find the general configuration of this product and other product channels under it. You can make modifications here.
              </Typography>
            </div>
            {isAllowed(userAccess, ['transaction_config_details.update']) && (
              <div className="controls">
                <button type="button" className="btn btn-secondary --disable-btn">
                  Disable for all merchants
                </button>
                {customConfigMerchants > 0 && (
                  <div className="custom-config-alert">
                    <i className="os-icon os-icon-alert-triangle" />
                    <span>{customConfigMerchants} merchants with custom configuration</span>
                    <button type="button" className="btn btn-link export-btn">
                      <i className="os-icon os-icon-download" />
                      Export
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="table-section">
            <section className="os-tabs-w">
              <div className="os-tabs-controls os-tabs-complex">
                <ul className="nav nav-tabs" role="tablist">
                  {Object.entries(TabInfo).map(([tab, value]) => (
                    <li
                      className="nav-item"
                      key={tab}
                      role="tab"
                      onClick={() => {
                        searchQuery.setQuery({ tab });
                      }}
                      onKeyDown={() => {
                        searchQuery.setQuery({ tab });
                      }}
                    >
                      <div className={`nav-link ${tab === activeTab ? 'active' : ''}`}>{capitalizeRemovedash(value)}</div>
                    </li>
                  ))}
                </ul>
              </div>
            </section>
            <div className="merchants-summary">
              <Typography variant="body1">{totalMerchants} merchants have access to USD Card Payments</Typography>
            </div>
            <Table
              annotation="Merchant(s) with access"
              className={merchantsCardPayments.className}
              data={merchantsCardPayments?.data || []}
              tableHeadings={['Merchant', 'Risk Level', 'Status', 'Configuration Type', 'Date Added']}
              loading={isLoading}
              renderFields
              hasPagination
              showCheckbox
              rowKey={merchantsCardPayments?.rowKey}
              rowURL={merchantsCardPayments?.rowURL}
              pageSize={data?.paging?.page_size}
              totalItems={data?.paging?.total_items}
              current={parseInt(page, 10)}
              hideTable={merchantsCardPayments?.data?.length === 0}
              filterKeywordPlaceholder="Search merchant..."
              tableWrapperClassName="table-responsive table-wrapper"
              emptyStateHeading={merchantsCardPayments?.emptyStateHeading || ''}
              emptyStateMessage={merchantsCardPayments.emptyStateMessage || ''}
              actionFn={currentPage => searchQuery.setQuery({ page: String(currentPage) })}
              limitAction={currentLimit => searchQuery.setQuery({ limit: String(currentLimit) })}
              filterHasAdvancedFilter={true}
              filterName="merchants have access to USD Card Payments"
              type="card-payments"
              filterShowExport={true}
              showDateFilter={true}
              isRowClickable={Boolean(isAllowed(userAccess, ['transaction_config_details.view']))}
            >
              {merchantsCardPayments.fields}
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardPayments;
