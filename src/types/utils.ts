import { switchCurrency } from '+utils';

export type CurrencyType = keyof typeof switchCurrency;

export type Nullable<T> = T | undefined | null;
export type SwappingType<T> = (array: Array<T>, a: number, b: number) => Array<T>;

export interface IErrorData {
  message?: string;
}

export interface IErrorResponse {
  data?: IErrorData;
  status?: number;
}

export type RequireOnlyOne<T, Keys extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> &
  {
    [K in Keys]-?: Required<Pick<T, K>> & Partial<Record<Exclude<Keys, K>, undefined>>;
  }[Keys];

export interface IError {
  data: {
    merchantEmail: {
      message: string;
    };
  };
  response?: IErrorResponse;
}
export interface UserAccess {
  'dashboard.view': boolean;
  'dashboard_analytics.view': boolean;
  'merchants.view': boolean;
  'merchants.export': boolean;
  'merchant_analytics.view': boolean;
  'merchant_general_details.view': boolean;
  'merchant_balances.view': boolean;
  'merchant_balance_history.view': boolean;
  'merchant_balance_history.export': boolean;
  'merchant_rolling_reserve_config.view': boolean;
  'merchant_rolling_reserve_config.update': boolean;
  'merchant_rolling_reserve_balance_history.view': boolean;
  'merchant_settlement_config.view': boolean;
  'merchant_settlement_config.update': boolean;
  'merchant_fee_config.view': boolean;
  'merchant_fee_config.update': boolean;
  'merchant_teams.view': boolean;
  'merchant_audit_logs.view': boolean;
  'merchant_status.update': boolean;
  'merchant_compliance.view': boolean;
  'merchant_compliance_details.view': boolean;
  'merchant_kyc_status.update': boolean;
  'merchant_support_email.update': boolean;
  'merchant_settlement_account.view': boolean;
  'merchant_settlement_account_status.update': boolean;
  'virtual_accounts.view': boolean;
  'virtual_account_status.update': boolean;
  'virtual_account_details.view': boolean;
  'pay_ins.view': boolean;
  'pay_ins.export': boolean;
  'pay_in_details.view': boolean;
  'payouts.view': boolean;
  'payouts.export': boolean;
  'payout_details.view': boolean;
  'payment_reversals.view': boolean;
  'payment_reversals.export': boolean;
  'settlements.view': boolean;
  'settlements.export': boolean;
  'settlement.approve': boolean;
  'settlement.process': boolean;
  'settlement_details.view': boolean;
  'settlement_payouts.view': boolean;
  'settlement_payouts.export': boolean;
  'webhooks.view': boolean;
  'webhooks.export': boolean;
  'audit_logs.view': boolean;
  'default_fee_config.view': boolean;
  'default_fee_config.update': boolean;
  'default_settlement_config.view': boolean;
  'default_settlement_config.update': boolean;
  'admin_users.view': boolean;
  'admin_users.export': boolean;
  'admin_user_details.view': boolean;
  'admin_users.update': boolean;
  'admin_user_permissions.view': boolean;
  'admin_user_permissions.update': boolean;
  'admin_users.delete': boolean;
  'admin_user_invitations.view': boolean;
  'admin_user_invitations.create': boolean;
  'admin_user_invitation.delete': boolean;
  'admin_user_audit_logs.view': boolean;
  'custom_roles.create': boolean;
  'my_custom_roles.view': boolean;
  'my_custom_roles.update': boolean;
  'custom_roles.view': boolean;
  'custom_roles.update': boolean;
  'third_party_reports.create': boolean;
  'third_party_reports.view': boolean;
  'metabase.view': boolean;
}

export type StatusConfigType = 'chargebacks' | 'default' | 'balance_funding';

export interface IStatusConfigObj {
  name: string;
  backgroundColor: string;
  color: string;
}

export type ISODateStringType = `${string}-${string}-${string}T${string}:${string}:${string}.${string}Z`;

export interface IStorage {
  checkAuthentication: () => boolean;
  checkExpiration: () => string | null;
  getRefreshToken: () => string;
  clientToken: () => string;
  removeItem: (key: string) => void;
  setItem: (key: string, value: string) => void;
  getItem: (key: string) => string | null;
}

export type moduleType =
  | 'settlements'
  | 'users'
  | 'roles'
  | 'invited_users'
  | 'reversals'
  | 'refunds'
  | 'issued-cards'
  | 'cards-transactions'
  | 'card-details-transactions'
  | 'issuance-chargebacks'
  | 'chargebacks'
  | 'settlement'
  | 'product-config'
  | 'issuing-merchants'
  | 'issuing_history'
  | 'bulk-payouts'
  | 'bulk-transactions'
  | 'merchant'
  | 'audit-logs'
  | 'settlements-payouts'
  | 'pay-in'
  | 'pay-outs'
  | 'partners_balance'
  | 'identity-merchants'
  | 'verification-events'
  | 'identity-billing'
  | 'payout'
  | 'payins'
  | 'settlements-details'
  | 'merchant-balance-history'
  | 'paused-payments'
  | 'paused_payments'
  | 'generate-report'
  | 'payment-preferences'
  | 'payout_reversals'
  | 'identity-requests'
  | 'teams'
  | 'merchant-team-members'
  | 'card-issuance-requesting-merchants'
  | 'product-config'
  | 'merchant-lien-history'
  | 'whitelisted-ip'
  | 'lien-event-history'
  | 'reconciliation'
  | 'merchant-compliance'
  | 'fixed-virtual-accounts'
  | 'upgrade-requests'
  | 'account-holders'
  | 'vba-transactions'
  | 'pool-accounts'
  | 'pool-account-summary'
  | 'merchant-registrations';

export type ExportActionType = {
  format: 'csv' | 'excel' | 'xlsx';
  close: () => void;
  fieldsToExport: string | string[];
};

export type OmitObjectKeyType<T, K extends keyof T> = Omit<T, K>;
export type RiskLevelType = `${'low' | 'medium' | 'above_average' | 'high'}_risk`;

export type PCIDSSLevelType = `level_${0 | 1 | 2 | 3 | 4}`;

export type ProcessorType = {
  id: number;
  name: string;
  meta: null;
  slug: string;
  is_active: boolean;
  channel: null;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
};
