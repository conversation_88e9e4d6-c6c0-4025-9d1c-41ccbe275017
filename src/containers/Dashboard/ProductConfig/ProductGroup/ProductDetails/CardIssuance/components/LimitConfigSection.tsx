import React, { useState } from 'react';
import { useQueryClient } from 'react-query';
import { useParams } from 'react-router-dom';

import Icon from '+containers/Dashboard/Shared/Icons';
import LimitDataTable from '+containers/Dashboard/Shared/LimitDataTable';
import Switch from '+containers/Dashboard/Shared/Switch';
import { useSetUserAccess } from '+hooks';
import {
  CardStatusTogglePayloadType,
  CategoryType,
  CurrencyType,
  EditMerchantLimitModalPayloadType,
  IssuingPartnerType,
  LimitType,
  MerchantLimitsConfigType,
  PartnerSwitchingPayloadType,
  PCIDSSLevelType,
  RiskLevelType,
  ToggleCardCreationPayloadType
} from '+types';
import { formatWithCommas, isAllowed } from '+utils';

import { issuingPartners } from '../constants/common';
import {
  formatMerchantFundingLimits,
  formatMerchantPCIDSSLimits,
  formatMerchantSpendingLimits,
  riskLevelMap
} from '../helpers/approvedMerchantDetailsHelpers';
import CardsStatusToggleModal from './CardsStatusToggleModal';
import EditMerchantLimitsModal from './EditMerchantLimitsModal';
import PartnerSwitchingModal from './PartnerSwitchingModal';
import SubscriptionFeeWaiverModal from './SubscriptionFeeWaiverModal';
import UpdateCardCreationModal from './UpdateCardCreationModal';

type ModalPurposeType =
  | 'limits_config'
  | 'toggling_card_creation'
  | 'switching_partner'
  | 'toggling_cards_access'
  | 'waive_subscription_fee'
  | null;

type ModalStateType<T> = {
  for: ModalPurposeType;
  action?: T | null;
  payload?:
    | EditMerchantLimitModalPayloadType
    | ToggleCardCreationPayloadType
    | CardStatusTogglePayloadType
    | PartnerSwitchingPayloadType
    | null;
};

type CardStatusToggleActionType = 'enable' | 'disable';

type ModalActionType = LimitType | CardStatusToggleActionType;

const initialModalState = {
  for: null,
  action: null,
  payload: null
} as const;

const LimitConfigSection = ({
  merchantInfo,
  category,
  config,
  pciDssLevel,
  riskLevel,
  provider,
  cardCreationToggleDisabled,
  cardAccessEnabled
}: {
  merchantInfo: { name: string; koraId: number; requestRef: string };
  category: CategoryType;
  config: MerchantLimitsConfigType;
  pciDssLevel: PCIDSSLevelType;
  riskLevel: RiskLevelType;
  provider: IssuingPartnerType;
  cardCreationToggleDisabled: boolean;
  cardAccessEnabled: boolean;
}) => {
  const queryClient = useQueryClient();
  const userAccess = useSetUserAccess();
  const canWaiveFee = isAllowed(userAccess, ['card_issuance_subscription_fee.update']);
  const canViewLimits = isAllowed(userAccess, ['card_issuance_limits.view']);
  const canUpdateLimits = isAllowed(userAccess, ['card_issuance_limits.update']);
  const { merchantId, currency } = useParams<{ merchantId: string; currency: CurrencyType }>();
  const [modalState, setModalState] = useState<ModalStateType<ModalActionType>>(initialModalState);
  const [cardCreationDisabled, setCardCreationDisabled] = useState(!config.card_creation_enabled);
  const isCustomerCardCategory = category === 'issued-cards';
  const formattedCardCategory: 'customer' | 'reserved' = isCustomerCardCategory ? 'customer' : 'reserved';

  const handleChangeModalState = ({ action, for: isOpen, payload }: ModalStateType<ModalActionType>) => {
    setModalState(prev => ({ ...prev, for: isOpen, action, payload }));
  };

  const closeModal = () => handleChangeModalState(initialModalState);

  const toggleCardCreation = (e: React.ChangeEvent<HTMLInputElement>) => setCardCreationDisabled(e.target.checked);

  const invalidateMerchantData = () => {
    queryClient.invalidateQueries(['ISSUING_MERCHANT_PLANS']);
    queryClient.invalidateQueries(['MERCHANT_DETAILS']);
    queryClient.invalidateQueries(['ISSUING_MERCHANTS']);
    queryClient.invalidateQueries(['REQUESTING_MERCHANTS']);
  };

  return (
    <>
      <section className="cards-config-panel">
        <div className="cards-config-panel__header">
          <div className="header__section d-flex flex-wrap justify-content-between align-items-start">
            <div className="cards-config-panel__toggle-creation">
              <h5 className="font-weight-bolder">
                {category === 'issued-cards' ? 'Issued Cards (Customers)' : 'Reserved Cards'} Configuration
              </h5>
              <div className={`toggle-creation__check ${cardCreationToggleDisabled ? 'disabled' : ''}`}>
                <label htmlFor="disable_card_creation" className="font-weight-bold">
                  Turn off card creation for this merchant
                </label>
                <input
                  id="disable_card_creation"
                  type="checkbox"
                  checked={cardCreationDisabled}
                  onChange={toggleCardCreation}
                  disabled={cardCreationToggleDisabled}
                />
              </div>

              {config.card_creation_enabled === cardCreationDisabled && (
                <div className="toggle-creation__button-container fade-in d-flex align-items-center mt-4">
                  <button
                    type="button"
                    className="btn btn-success px-4"
                    onClick={() =>
                      handleChangeModalState({
                        for: 'toggling_card_creation',
                        payload: {
                          card_category: formattedCardCategory,
                          action: cardCreationDisabled ? 'disable' : 'enable',
                          reference: merchantId
                        }
                      })
                    }
                  >
                    Save
                  </button>
                  <span className="divider-x" />
                  <button type="button" className="btn btn-link font-weight-bold" onClick={() => setCardCreationDisabled(prev => !prev)}>
                    Cancel
                  </button>
                </div>
              )}
            </div>
            <div className="d-flex align-items-center">
              <Switch
                checked={cardAccessEnabled}
                onCheckedChange={() =>
                  handleChangeModalState({
                    for: 'toggling_cards_access',
                    action: cardAccessEnabled ? 'disable' : 'enable',
                    payload: {
                      currency,
                      kora_id: merchantInfo.koraId,
                      access_request_reference: merchantId,
                      card_category: formattedCardCategory
                    }
                  })
                }
                id="enableCards"
              />
              <label htmlFor="enableCards" className="my-0 ml-2">
                Enable {formattedCardCategory} cards
              </label>
            </div>
          </div>

          {cardCreationToggleDisabled && (
            <div className="cards-config-panel__access-message d-flex align-items-center">
              <Icon name="info" fill="#2376F3" stroke="#fff" />
              <span>
                Access to {formattedCardCategory === 'reserved' ? 'Reserved Virtual Cards (RVCs)' : 'Customer Cards'} is disabled for this
                merchant.
              </span>
            </div>
          )}
        </div>
        <div className="cards-config-panel__main">
          <details className="cards-config-panel__accordion cards-config-panel__change-partner">
            <summary className="cards-config-panel__accordion-summary" style={{ fontSize: '1rem' }}>
              Issuing Partner
            </summary>

            <div className="cards-config-panel__container setting-row">
              <div className="setting-row__content">
                <p className="setting-row__description">
                  This is the current partner managing card issuance for this merchant. All Reserved Virtual Cards are issued and maintained
                  under their system.
                </p>
                <p className="setting-row__status">
                  Current issuing partner: <strong>{issuingPartners[provider] ?? 'Not Available'}</strong>
                </p>
              </div>

              <div className="setting-row__actions">
                <button
                  className="text-decoration-none btn btn-link setting-row__actions-link"
                  onClick={() =>
                    handleChangeModalState({
                      for: 'switching_partner',
                      payload: { merchantName: merchantInfo.name, merchantReference: merchantId }
                    })
                  }
                  style={{ fontWeight: 600 }}
                  type="button"
                  disabled={cardCreationToggleDisabled}
                >
                  Change Partner
                </button>
              </div>
            </div>
          </details>

          <details className="cards-config-panel__accordion cards-config-panel__change-partner">
            <summary className="cards-config-panel__accordion-summary" style={{ fontSize: '1rem' }}>
              Subscription Fee
            </summary>

            <div className="cards-config-panel__container setting-row">
              <div className="setting-row__content">
                <p className="setting-row__description">
                  This is where we can customize whether to apply or waive the subscription fee for this merchant.
                </p>
                <p className="setting-row__status">
                  {isCustomerCardCategory ? 'Issued Cards' : 'Reserved Cards'} subscription fee:{' '}
                  <strong> {config.monthly_billing_enabled ? 'Applied' : 'Waived'}</strong>
                </p>
              </div>

              {canWaiveFee && (
                <div className="setting-row__actions">
                  <button
                    className="text-decoration-none btn btn-link setting-row__actions-link"
                    onClick={() =>
                      handleChangeModalState({
                        for: 'waive_subscription_fee',
                        payload: { merchantName: merchantInfo.name, merchantReference: merchantId }
                      })
                    }
                    style={{ fontWeight: 600 }}
                    type="button"
                    disabled={cardCreationToggleDisabled}
                  >
                    Customize
                  </button>
                </div>
              )}
            </div>
          </details>

          {canViewLimits && (
            <details className="cards-config-panel__accordion">
              <summary className="cards-config-panel__accordion-summary">Limits</summary>

              <div className="cards-config-panel__limits">
                {category === 'issued-cards' ? (
                  <LimitDataTable
                    aria-label="edit PCI DSS limits"
                    textAlign="right"
                    actionText={config.pcidss_level_limit.type === 'custom' ? 'Edit' : 'Customize'}
                    data={[
                      ['Merchant PCI DSS Level', <div style={{ color: '#374151' }}>{pciDssLevel?.replace('level_', 'Level ')}</div>],
                      ['Transaction Count Limit', formatMerchantPCIDSSLimits(config.pcidss_level_limit.data).yearlyTransactionCount],
                      ['No. of Issuable Cards', formatMerchantPCIDSSLimits(config.pcidss_level_limit.data).yearlyIssuedCards]
                    ]}
                    onActionClick={() =>
                      handleChangeModalState({
                        for: 'limits_config',
                        action: 'pci_dss',
                        payload: { pciDssLevel: pciDssLevel, ...config.pcidss_level_limit.data, type: config.pcidss_level_limit.type }
                      })
                    }
                    headings={['', '']}
                    description="This is the pci dss level limit configuration for this merchant"
                    title="Limits based on PCI DSS Level"
                    actionIsDisabled={cardCreationToggleDisabled}
                    hideAction={!canUpdateLimits}
                  />
                ) : (
                  <LimitDataTable
                    aria-label="edit card and transaction count limits"
                    textAlign="right"
                    actionText={config.pcidss_level_limit.type === 'custom' ? 'Edit' : 'Customize'}
                    data={[
                      [
                        'Transaction Count Limit',
                        <div style={{ color: '#374151' }}>{formatWithCommas(config.pcidss_level_limit.data.yearly_transaction_count)}</div>
                      ],
                      ['No. of Issuable Cards', formatWithCommas(config.pcidss_level_limit.data.yearly_issued_cards)]
                    ]}
                    onActionClick={() =>
                      handleChangeModalState({
                        for: 'limits_config',
                        action: 'pci_dss',
                        payload: { ...config.pcidss_level_limit.data, type: config.pcidss_level_limit.type }
                      })
                    }
                    headings={['', '']}
                    description="These are the card and transaction count limit configuration for this merchant"
                    title="Card and Transaction Count Limits"
                    actionIsDisabled={cardCreationToggleDisabled}
                    hideAction={!canUpdateLimits}
                  />
                )}

                <LimitDataTable
                  aria-label="edit funding limits"
                  actionText={config.risk_level_limit.funding_limit.type === 'custom' ? 'Edit' : 'Customize'}
                  textAlign="right"
                  data={[
                    [
                      'Merchant Risk Level',
                      <div
                        style={{
                          color: '#374151'
                        }}
                      >
                        {riskLevelMap[riskLevel]}
                      </div>
                    ],
                    ['Max. funding limit per day', formatMerchantFundingLimits(config.risk_level_limit.funding_limit.data).dailyLimit],
                    ['Max. funding limit per month', formatMerchantFundingLimits(config.risk_level_limit.funding_limit.data).monthlyLimit],
                    [
                      'Max. funding limit per quarter',
                      formatMerchantFundingLimits(config.risk_level_limit.funding_limit.data).quarterlyLimit
                    ]
                  ]}
                  onActionClick={() =>
                    handleChangeModalState({
                      for: 'limits_config',
                      action: 'funding',
                      payload: {
                        riskLevel,
                        ...config.risk_level_limit.funding_limit.data,
                        type: config.risk_level_limit.funding_limit.type
                      }
                    })
                  }
                  headings={['', '']}
                  description="These are the card funding limit configurations for this merchant"
                  title="Card Funding Limits"
                  actionIsDisabled={cardCreationToggleDisabled}
                  hideAction={!canUpdateLimits}
                />

                <LimitDataTable
                  aria-label="edit spending limits"
                  actionText={config.risk_level_limit.spending_limit.type === 'custom' ? 'Edit' : 'Customize'}
                  textAlign="right"
                  data={[
                    [
                      'Merchant Risk Level',
                      <div
                        style={{
                          color: '#374151'
                        }}
                      >
                        {riskLevelMap[riskLevel]}
                      </div>
                    ],
                    [
                      'Max. limit per transaction',
                      formatMerchantSpendingLimits(config.risk_level_limit.spending_limit.data).perTransactionMax
                    ],
                    ['Daily transaction cap', formatMerchantSpendingLimits(config.risk_level_limit.spending_limit.data).dailyMax],
                    ['Monthly transaction cap', formatMerchantSpendingLimits(config.risk_level_limit.spending_limit.data).monthlyMax]
                  ]}
                  onActionClick={() =>
                    handleChangeModalState({
                      for: 'limits_config',
                      action: 'spending',
                      payload: {
                        riskLevel,
                        ...config.risk_level_limit.spending_limit.data,
                        type: config.risk_level_limit.spending_limit.type
                      }
                    })
                  }
                  headings={['', '']}
                  description="These are the card spending limit configurations for this merchant"
                  title="Card Spending Limits"
                  actionIsDisabled={cardCreationToggleDisabled}
                  hideAction={!canUpdateLimits}
                />
              </div>
            </details>
          )}
        </div>
      </section>

      {modalState.for === 'limits_config' && (
        <EditMerchantLimitsModal
          limitType={modalState.action as LimitType}
          cardCategory={formattedCardCategory}
          onClose={closeModal}
          refetchLimits={invalidateMerchantData}
          defaultValues={modalState.payload as EditMerchantLimitModalPayloadType}
          merchantId={merchantId}
        />
      )}

      {modalState.for === 'toggling_card_creation' && (
        <UpdateCardCreationModal
          onClose={closeModal}
          onToggleSuccess={invalidateMerchantData}
          payload={modalState.payload as ToggleCardCreationPayloadType}
        />
      )}

      {modalState.for === 'toggling_cards_access' && (
        <CardsStatusToggleModal
          action={modalState.action as CardStatusToggleActionType}
          onClose={closeModal}
          cardCategory={formattedCardCategory}
          payload={modalState.payload as CardStatusTogglePayloadType}
          onToggleSuccess={invalidateMerchantData}
        />
      )}

      {modalState.for === 'switching_partner' && (
        <PartnerSwitchingModal
          cardCategory={formattedCardCategory}
          currentPartner={provider}
          payload={modalState.payload as PartnerSwitchingPayloadType}
          onClose={closeModal}
          onToggleSuccess={invalidateMerchantData}
        />
      )}

      {modalState.for === 'waive_subscription_fee' && (
        <SubscriptionFeeWaiverModal
          onClose={closeModal}
          type="merchant"
          merchantDetails={{ name: merchantInfo.name, reference: merchantId }}
          cardCategory={category === 'issued-cards' ? 'customer' : 'reserved'}
          feeIsActive={config.monthly_billing_enabled}
          currency={currency}
          onToggleSuccess={invalidateMerchantData}
        />
      )}
    </>
  );
};

export default LimitConfigSection;
