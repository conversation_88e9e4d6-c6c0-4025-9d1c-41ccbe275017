import { useSearchQuery } from '+hooks';
import React from 'react';

import './FilterSection.scss';

interface IFilterSectionProps {
  totalMerchants: number;
  onSearch: (query: any) => void;
  loading?: boolean;
}

const FilterSection: React.FC<IFilterSectionProps> = ({ totalMerchants, onSearch, loading = false }) => {
  const searchQuery = useSearchQuery();
  
  const handleFilterChange = (key: string, value: string) => {
    searchQuery.setQuery({ [key]: value, page: '1' });
  };

  const handleGoClick = () => {
    // Trigger search with current filters
    if (onSearch) {
      onSearch(searchQuery.value);
    }
  };

  return (
    <div className="enhanced-filter-section">
      <div className="filter-row">
        <div className="search-control">
          <div className="search-input-wrapper">
            <i className="os-icon os-icon-search search-icon" />
            <input
              type="text"
              placeholder="Search merchant..."
              value={searchQuery.value.keyword || ''}
              onChange={(e) => handleFilterChange('keyword', e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        <div className="filter-controls">
          <div className="filter-dropdown-wrapper">
            <select
              value={searchQuery.value.status || 'all'}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="filter-dropdown"
            >
              <option value="all">All Statuses</option>
              <option value="enabled">Enabled</option>
              <option value="disabled">Disabled</option>
            </select>
            <i className="os-icon os-icon-chevron-down dropdown-icon" />
          </div>

          <div className="filter-dropdown-wrapper">
            <select
              value={searchQuery.value.selectedConfig || 'all'}
              onChange={(e) => handleFilterChange('selectedConfig', e.target.value)}
              className="filter-dropdown"
            >
              <option value="all">All Configuration</option>
              <option value="default">Default</option>
              <option value="custom">Custom</option>
            </select>
            <i className="os-icon os-icon-chevron-down dropdown-icon" />
          </div>

          <div className="date-filter-wrapper">
            <div className="date-input-wrapper">
              <i className="os-icon os-icon-calendar date-icon" />
              <input
                type="text"
                placeholder="From"
                value={searchQuery.value.dateFrom || ''}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                className="date-input"
                readOnly
              />
            </div>
          </div>

          <div className="date-filter-wrapper">
            <div className="date-input-wrapper">
              <i className="os-icon os-icon-calendar date-icon" />
              <input
                type="text"
                placeholder="To"
                value={searchQuery.value.dateTo || ''}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                className="date-input"
                readOnly
              />
            </div>
          </div>

          <button
            type="button"
            className="go-button"
            onClick={handleGoClick}
            disabled={loading}
          >
            <i className="os-icon os-icon-arrow-right" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterSection;
