import { CurrencyType } from './utils';

export type DisputesType = 'refunds' | 'reversals' | 'chargebacks';

export type SummaryItemType = { label: React.ReactNode; value: React.ReactNode; hidden?: boolean };

export type StatusItemType = { status: React.ReactNode; statusBg?: string; statusColor?: string; hide?: boolean };

export interface IBaseDisputesData {
  reference: string;
  amount: string;
  status: string;
  payment_reversal_payouts: { id: string; created_at: string; reference: string }[];
}

export type ActionButtonType = {
  children: React.ReactNode;
  className?: string;
  onClick: () => void;
  variant?: 'primary' | 'light-blue' | 'text-button' | 'danger';
  disabled?: boolean;
  hidden?: boolean;
  iconAfter?: string | React.ReactNode;
  iconBefore?: string | React.ReactNode;
};

export type SummaryGeneratorFnType<T> = (dataItem: T, ...args: any[]) => Array<SummaryItemType>;

export type ChildrenGeneratorFnType<T> = (dataItem: T, ...args: any[]) => React.ReactNode;

export interface IDisputes<S extends IBaseDisputesData, T extends IBaseDisputesData, U extends IBaseDisputesData> {
  tabs?: Array<DisputesType>;
  currency?: CurrencyType;
  disputesGenerators?: Partial<Record<DisputesType, Array<Partial<S | T | U>>>> | null;
  summaryGenerators: Partial<Record<DisputesType, SummaryGeneratorFnType<S | T | U>>>;
  childrenGenerators?: Partial<Record<DisputesType, ChildrenGeneratorFnType<S | T | U>>>;
  actionGenerator?: Partial<Record<DisputesType, (dataItem: S | T | U, ...args: any[]) => React.ReactNode>>;
  isPayOut?: boolean;
}

export type TTransactionDetails = {
  [key in
    | 'reference'
    | 'status'
    | 'amount'
    | 'amount_charged'
    | 'amount_paid'
    | 'amount_collected'
    | 'fee'
    | 'vat'
    | 'narration'
    | 'cross_currency'
    | 'description'
    | 'type'
    | 'external_reference'
    | 'payment_source_type'
    | 'payment_source_id'
    | 'payment_id'
    | 'currency'
    | 'channel'
    | 'message'
    | 'processor'
    | 'processor_reference'
    | 'transaction_date'
    | 'completed_at'
    | 'created_at'
    | 'trace_id'
    | 'payment_destination_type'
    | 'unique_reference'
    | 'payment_reversals'
    | 'status_history'
    | 'status_reason'
    | 'reversal_reason'
    | 'merchant_email'
    | 'payment_source_reference'
    | 'approved_amount'
    | 'merchant_reference'
    | 'merchant'
    | 'date']: string;
} & {
  [key in 'payment_reversals_type' | 'merchant_id' | 'account_id' | 'card_acceptor_name' | 'card_acceptor_country']: number;
} & {
  [key in 'merchant_bears_cost' | 'can_reverse_payment']: boolean;
} & {
  batch_reference: null;
  processor_messages: null;
} & {
  metadata: {
    trace_id: string;
  };

  auth_data: {
    pre_auth_expiry: string;
  };

  payout_reversal: {
    id: string;
  };

  payment_source: {
    [key in 'processor' | 'processor_reference' | 'reference' | 'channel' | 'amount_collected' | 'amount' | 'status']: string;
  } & { settlement: { reference: string } };

  meta: {
    receipt: string;
    stan: string;
    authorization_code: string;
    additional_fees: {
      stamp_duty: string;
    };
  } | null;

  card: { [key in 'provider' | 'reference' | 'last_four' | 'brand' | 'expiration_date']: string } & {
    reserved: boolean;

    card_holder: {
      [key in 'first_name' | 'last_name']: string;
    };
  };

  payment: {
    unique_reference: string;
    reference: string;
    sentinal_transaction: { [key in 'processing_fee' | 'vat']: string } | null;
    description: string;

    customer: {
      email: string | null;
      name: string;
    } & { id: number };

    account: {
      id: number;
      name: string;
    };
  };

  account: {
    name: string;
  };

  destination: {
    type: string;

    details: {
      [key in 'bank_name' | 'account_number' | 'account_name']: string;
    };
  };

  source: {
    type: string;

    details: {
      [key in 'bank_name' | 'account_number' | 'account_name' | 'bank_slug']: string;
    };

    virtual_bank_account: {
      [key in 'bank_name' | 'account_number' | 'account_name' | 'expiry']: string;
    };
  };

  remittance_data: {
    type: string;
    sender_name: string;
    source_of_funds: string;
    sender_phone_number: number;
    sender_nationality?: string;
    sender_country_iso?: string;
    sender_service_provider_name?: string;
    destination_phone_number?: number;
    remittance_purpose?: string;
    sender_occupation?: string;
    sender_dob?: string;
    sender_id_type?: string;
    sender_id_number?: number;
  };
};

export type useTransactionStatusStateType = {
  openTransactionStatusModal?: boolean;
  activeTransaction?: any;
  clickedTransactionIds?: any[];
};

export type TRelatedTransactions = {
  [key in 'reference' | 'status' | 'amount' | 'amount_charged' | 'fee' | 'vat' | 'currency' | 'transaction_date' | 'completed_at']: string;
} & { meta: { charge_operation: string } };

export type TConversionsTrxn = {
  [key in
    | 'source_currency'
    | 'destination_currency'
    | 'exchange_rate'
    | 'source_amount'
    | 'converted_amount'
    | 'status'
    | 'reference'
    | 'channel'
    | 'narration'
    | 'transaction_date'
    | 'provider'
    | 'type'
    | 'unique_reference'
    | 'service_charge']: string;
} & {
  customer_name: string | null;
  customer_email: string | null;
} & {
  account: {
    name: string;
    email: string;
  };
} & {
  source?: {
    [key in 'type' | 'reference' | 'status' | 'amount' | 'currency' | 'transaction_date']: string;
  };
};
