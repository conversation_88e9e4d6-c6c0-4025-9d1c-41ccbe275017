/* eslint-disable import/prefer-default-export */
import { http, HttpResponse } from 'msw';

import {
  mockCardIssuanceLimits,
  mockChargebackHistoryData,
  mockChargebackSummaryData,
  mockedAccountHolderEvent,
  mockedAccountHolderKycDetail,
  mockedAccountHolderKycHistory,
  mockedAllVbaAccountUpgradeReq,
  mockedAllVbaHolders,
  mockedAllVbaNumbers,
  mockedAuditLogs,
  mockedBankData,
  mockedBankTrxPaymentPreference,
  mockedBankTrxPaymentPreferenceList,
  mockedBulkpayouts,
  mockedBulk<PERSON><PERSON><PERSON>y,
  mockedBulkTransactionList,
  mockedCardIssuanceBillingCycle,
  mockedCardIssuancePlans,
  mockedCardIssuedDetails,
  mockedCardTransactionsData,
  mockedConversions,
  mockedCountriesData,
  mockedCurrenciesData,
  mockedCurrenciesExchangeRate,
  mockedCurrency<PERSON>onfigD<PERSON>,
  mockedCustomer<PERSON><PERSON>D<PERSON>,
  mocked<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  mockedCustom<PERSON><PERSON><PERSON>oleAssignees,
  mocked<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  mockedDisputeChargeBack,
  mockedDisputeRefundDetailsData,
  mockedEmailConfigurations,
  mockedGetAllMerchants,
  mockedGetSentinalStatus,
  mockedInitiateSettlementUploadResponse,
  mockedInvitedUsers,
  mockedIssuanceChargebacksData,
  mockedIssuedMerchantDetails,
  mockedIssuedMerchantTabQueries,
  mockedIssuedWalletHistoryData,
  mockedLienDetailsData,
  mockedMerchantEmailDetails,
  mockedMerchantKyc,
  mockedMerchantRegistration,
  mockedMerchantsData,
  mockedMerchantsDataList,
  mockedMerchantStatistics,
  mockedNgnVbaNumberDetails,
  mockedPartnerAccountBalance,
  mockedPartnerBalanceList,
  mockedPartnerBalanceTransactionHistory,
  mockedPartnersAccountBalances,
  mockedPayins,
  mockedPayOutMetabaseData,
  mockedPermissionsData,
  mockedPoolAccounts,
  mockedPoolAccountTransactionDetails,
  mockedPoolAccountTransactions,
  mockedProcessorQuery,
  mockedReconciliationHistory,
  mockedReservedCardsData,
  mockedSettelementReconciliationHistory,
  mockedSettlementProcessorConfig,
  mockedSettlements,
  mockedSettlementsSummary,
  mockedTeamMembers,
  mockedTransactionDetailsData,
  mockedTransactionLimits,
  mockedUserDetails,
  mockedUserRole,
  mockedUserRoles,
  mockedUsers,
  mockedVbaHolder,
  mockedVbaNumberTransactions,
  mockedVbaOverview,
  mockedVbaPayins,
  mockedVerificationDetails,
  mockedVerificationsData,
  mockIssuingMerchantDetails,
  mockPaymentReversalDetails,
  mockPayOutData,
  mockProcessorData,
  mockSpoolingReportData,
  mockUnlockMerchant,
  mockWhiteListedIPData
} from '+mock/mockData';

export const handlers = [
  http.get('http://localhost/admin/transactions/payouts', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('/admin/metabase/', () => {
    return HttpResponse.json(mockedPayOutMetabaseData, { status: 200 });
  }),
  http.get('/admin/processors', () => {
    return HttpResponse.json(mockProcessorData, { status: 200 });
  }),
  http.get('/admin/settlements/payouts', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('/admin/payment-reversals/KPY-RFD-LeL3pMoUktch', () => {
    return HttpResponse.json(mockPaymentReversalDetails, { status: 200 });
  }),
  http.get('/admin/webhooks/merchants', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('/admin/merchants/filter-options', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('http://localhost/admin/virtual-bank-account/undefined', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('/admin/merchants/tier-levels', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('http://localhost/admin/virtual-bank-account/undefined/transactions', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('http://localhost/admin/transactions/payouts/undefined', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('/admin/transactions/analytics/count/status', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('/admin/transactions/analytics/count', ({ params }) => {
    const { type } = params;
    let response;

    if (type === 'collection' || type === 'disbursement' || type === 'revenue') {
      response = mockPayOutData;
    } else {
      response = {};
    }
    return HttpResponse.json(response, { status: 200 });
  }),
  http.get('http://localhost/admin/processors/analytics/balance/monnify', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('http://localhost/admin/fees/analytics/net', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('http://localhost/admin/fees/analytics/gross', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('http://localhost/admin/merchants/analytics/merchants/count', () => {
    return HttpResponse.json(mockPayOutData, { status: 200 });
  }),
  http.get('/admin/misc/currencies', () => {
    return HttpResponse.json(mockedCurrenciesData, { status: 200 });
  }),
  http.get('/admin/misc/banks', () => {
    return HttpResponse.json(mockedBankData, { status: 200 });
  }),
  http.get('/admin/misc/countries', () => {
    return HttpResponse.json(mockedCountriesData, { status: 200 });
  }),
  http.get('/admin/notifications/config', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/bulk-actions', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.post('/admin/bulk-actions', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/notifications', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/notification-configurations/1', () => {
    return HttpResponse.json(mockedEmailConfigurations, { status: 200 });
  }),
  http.get('/admin/notification-configurations/2', () => {
    return HttpResponse.json({ message: 'error occured' }, { status: 400 });
  }),
  http.get('/admin/merchants/1', () => {
    return HttpResponse.json(mockedMerchantEmailDetails, { status: 200 });
  }),
  http.get('/admin/merchants/1', () => {
    return HttpResponse.json(mockedMerchantEmailDetails, { status: 200 });
  }),
  http.get('/admin/merchants/2', () => {
    return HttpResponse.json({ message: 'error occured' }, { status: 400 });
  }),
  http.get('/admin/merchants/teams/617', () => {
    return HttpResponse.json(mockedTeamMembers, { status: 200 });
  }),

  http.patch('http://localhost/admin/merchants/unlock/846', () => {
    return HttpResponse.json(
      {
        status: true,
        message: 'User account unlocked successfully',
        data: null
      },
      { status: 200 }
    );
  }),
  http.get('/admin/transactions/payins/6KsdgnKp65pK', () => {
    return HttpResponse.json(mockedTransactionDetailsData, { status: 200 });
  }),
  http.get('/admin/transactions/payments/undefined', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/transactions/payins/d3xXH8FNthokMYi/reversals', () => {
    return HttpResponse.json(
      {
        data: {
          reversals: [],
          can_reverse_payment: true
        }
      },
      { status: 200 }
    );
  }),
  http.get('/admin/transactions/bulk/KPY-BPO-2024013117543PZ80411375/payouts', () => {
    return HttpResponse.json(mockedBulkTransactionList, { status: 200 });
  }),
  http.get('/admin/transactions/bulk/KPY-BPO-2024013117543PZ80411375', () => {
    return HttpResponse.json(mockedBulkSummary, { status: 200 });
  }),
  http.get('http://localhost/admin/transactions/bulk-transfer', () => {
    return HttpResponse.json(mockedBulkpayouts, { status: 200 });
  }),
  http.get('/admin/audit', () => {
    return HttpResponse.json(mockedAuditLogs, { status: 200 });
  }),
  http.get('/admin/settlements', () => {
    return HttpResponse.json(mockedSettlements, { status: 200 });
  }),
  http.get('/admin/settlements/summary', () => {
    return HttpResponse.json(mockedSettlementsSummary, { status: 200 });
  }),
  http.get('/admin/transactions/payins', () => {
    return HttpResponse.json(mockedPayins, { status: 200 });
  }),
  http.get('/admin/liens/KPY-LIEN-chGzjolFFqsU0Bm', () => {
    return HttpResponse.json(mockedLienDetailsData, { status: 200 });
  }),
  http.get('/admin/refunds/KPY-RFD-d2mu4S16oS9j', () => {
    return HttpResponse.json(mockedDisputeRefundDetailsData, { status: 200 });
  }),
  http.get('/admin/chargebacks/KPY-CHG-0bx0UUagNBFY81V', () => {
    return HttpResponse.json(mockedDisputeChargeBack, { status: 200 });
  }),
  http.patch('/admin/refunds/KPY-RFD-d2mu4S16oS9j/status', () => {
    return HttpResponse.json({ data: 'success' }, { status: 200 });
  })
];

export const usersHandlers = [
  http.get('http://localhost/admin/invites', () => {
    return HttpResponse.json(mockedInvitedUsers, { status: 200 });
  }),
  http.get('/admin/users', () => {
    return HttpResponse.json(mockedUsers, { status: 200 });
  }),
  http.get('/admin/permissions', () => {
    return HttpResponse.json(mockedPermissionsData, { status: 200 });
  }),
  http.get('/admin/user-roles', () => {
    return HttpResponse.json(mockedUserRoles, { status: 200 });
  }),
  http.get('/admin/user-roles/6', () => {
    return HttpResponse.json(mockedUserRole, { status: 200 });
  }),
  http.get('/admin/user-roles/121', () => {
    return HttpResponse.json(mockedCustomUserRole, { status: 200 });
  }),
  http.get('/admin/user-roles/121/users', () => {
    return HttpResponse.json(mockedCustomUserRoleAssignees, { status: 200 });
  }),
  http.get('/admin/users/471', () => {
    return HttpResponse.json(mockedUserDetails, { status: 200 });
  }),
  http.post('/admin/invites', () => {
    return HttpResponse.json(
      {
        data: {
          id: 74,
          email: '<EMAIL>',
          role_id: 127,
          expiry_date: '2023-02-20T15:52:33.375Z',
          code: 'aiRo7oKZzWNk',
          status: 'pending',
          admin_id: '503',
          updatedAt: '2023-02-19T20:25:53.390Z',
          createdAt: '2023-02-19T20:25:53.390Z'
        }
      },
      { status: 200 }
    );
  })
];

export const chargebacksHandlers = [
  http.get('http://localhost/admin/chargebacks', () => {
    return HttpResponse.json(mockChargebackHistoryData, { status: 200 });
  }),
  http.get('http://localhost/admin/chargebacks/summary', () => {
    return HttpResponse.json(mockChargebackSummaryData, { status: 200 });
  })
];

export const cardIssuanceHandlers = [
  http.get('http://localhost/admin/cards', () => {
    return HttpResponse.json(
      {
        data: {
          total_cards: 62,
          total_active_cards: 53
        }
      },
      { status: 200 }
    );
  }),
  http.get('http://localhost/admin/cards/statistics', () => {
    return HttpResponse.json(
      {
        data: {
          total_cards: 62,
          total_active_cards: 53
        }
      },
      { status: 200 }
    );
  }),
  http.get('http://localhost/admin/transactions/card-issuance/statistics', () => {
    return HttpResponse.json(
      {
        data: {
          total_transactions: 0,
          total_transactions_value: 0
        }
      },
      { status: 200 }
    );
  }),
  http.get('/admin/cards/1', () => {
    return HttpResponse.json(mockedCardIssuedDetails, { status: 200 });
  }),
  http.get('/admin/cards/dashboard', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/merchants/card-issuance/1234567890', () => {
    return HttpResponse.json(mockedIssuedMerchantDetails, { status: 200 });
  }),
  http.get('/admin/transactions/card-issuance/wallets/123', () => {
    return HttpResponse.json(mockedIssuedWalletHistoryData, { status: 200 });
  }),
  http.get('/admin/processors', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/transactions/card-issuance/wallets/1234567890', () => {
    return HttpResponse.json(mockedIssuedMerchantDetails, { status: 200 });
  }),
  http.get('/admin/transactions/card-issuance/wallets/deposit-requests', () => {
    return HttpResponse.json(mockedIssuedMerchantTabQueries, { status: 200 });
  }),
  http.get('/admin/cards', ({ params }) => {
    const merchantId = params.merchantKoraId;
    const { cardCategory } = params;
    let response;

    if (merchantId) {
      if (cardCategory === 'customer') response = mockedCustomerCardsData;
      else response = mockedReservedCardsData;
    }
    return HttpResponse.json(response, { status: 200 });
  }),
  http.get('/admin/transactions/card-issuance', ({ params }) => {
    const merchantId = params.merchantKoraId;
    let response;

    if (merchantId) response = mockedCardTransactionsData;
    return HttpResponse.json(response, { status: 200 });
  }),
  http.get('/admin/chargebacks/card-issuance', ({ params }) => {
    const merchantId = params.merchantKoraId;
    let response;

    if (merchantId) response = mockedIssuanceChargebacksData;

    return HttpResponse.json(response, { status: 200 });
  })
];

export const conversionsHandlers = [
  http.get('/admin/conversions', () => {
    return HttpResponse.json(mockedCurrenciesExchangeRate, { status: 200 });
  }),
  http.get('/admin/conversions/markups/', () => {
    return HttpResponse.json({ data: [] }, { status: 200 });
  }),
  http.patch('http://localhost/admin/conversions/markups/', () => {
    return HttpResponse.json({ data: [] }, { status: 200 });
  })
];
export const productConfigHandlers = [
  http.get('/admin/settings/merchants/currency', () => {
    return HttpResponse.json({ GHS: 533, KES: 68, NGN: 534, USD: 56, ZAR: 534 }, { status: 200 });
  }),
  http.get('/admin/settings/merchants', () => {
    return HttpResponse.json(mockedMerchantsData, { status: 200 });
  }),
  http.get('/admin/settings/merchants/configuration', () => {
    return HttpResponse.json(mockedCurrencyConfigData, { status: 200 });
  }),
  http.get('/admin/merchants/card-issuance/KPY12345', () => {
    return HttpResponse.json(mockIssuingMerchantDetails, { status: 200 });
  }),
  http.patch('/admin/merchants/card-issuance/KPY12345/pci-dss', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/settings/card-issuance/limits', () => {
    return HttpResponse.json(mockCardIssuanceLimits, { status: 200 });
  }),
  http.patch('admin/merchants/card-issuance/KPY12345/card-creation', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.patch('/admin/merchants/card-issuance/KPY12345/switch-provider', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.patch('/admin/card-issuance/access/', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.patch('admin/merchants/card-issuance/fees', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.patch('admin/settings/card-issuance/wallet-funding', () => {
    return HttpResponse.json({}, { status: 200 });
  })
];
export const verificationEventsHandlers = [
  http.get('http://localhost/admin/identities/verifications', () => {
    return HttpResponse.json(mockedVerificationsData, { status: 200 });
  }),
  http.get('http://localhost/admin/identities/verification/123', () => {
    return HttpResponse.json(mockedVerificationDetails, { status: 200 });
  })
];

export const spoolingReportHandlers = [
  http.get('/admin/processors/reports', () => {
    return HttpResponse.json(mockSpoolingReportData, { status: 200 });
  }),
  http.post('/admin/processors/reports', () => {
    return HttpResponse.json({}, { status: 200 });
  })
];
export const merchantHandlers = [
  http.post('/admin/merchants/statement/617', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/merchants/1/kyc', () => {
    return HttpResponse.json(mockedMerchantKyc, { status: 200 });
  }),
  http.get('/admin/merchants/2/kyc', () => {
    return HttpResponse.json(mockedMerchantKyc, { status: 200 });
  }),
  http.get('/admin/tax-management/1', () => {
    return HttpResponse.json(mockedGetSentinalStatus, { status: 200 });
  }),
  http.get('/admin/tax-management/2', () => {
    return HttpResponse.json({ id: 2, identifier: 'two', status: 'PENDING', enabled: false }, { status: 200 });
  }),
  http.post('/admin/tax-management/1', () => {
    return HttpResponse.json({}, { status: 200 });
  }),

  http.put('/admin/tax-management/1', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.get('/admin/merchants', () => {
    return HttpResponse.json(mockedGetAllMerchants, { status: 200 });
  }),
  http.patch('/admin/merchants/user-management/846', ({ params }) => {
    const unlock = params.action;
    let response;
    if (unlock) response = mockUnlockMerchant;

    return HttpResponse.json(response, { status: 200 });
  }),
  http.get('/admin/merchants/1/ip-addresses', () => {
    return HttpResponse.json(mockWhiteListedIPData, { status: 200 });
  })
];

export const identityMerchantHandlers = [
  http.get('http://localhost/admin/merchants', () => {
    return HttpResponse.json(mockedMerchantsDataList, { status: 200 });
  })
];

export const vbaHanders = [
  http.get('/admin/virtual-bank-account/overview', () => {
    return HttpResponse.json(mockedVbaOverview, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account', () => {
    return HttpResponse.json(mockedAllVbaNumbers, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/upgrade-requests', () => {
    return HttpResponse.json(mockedAllVbaAccountUpgradeReq, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/account-holders/test', () => {
    return HttpResponse.json(mockedVbaHolder, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/account-holders/test/kyc-details', () => {
    return HttpResponse.json(mockedAccountHolderKycDetail, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/account-holders/test/events', () => {
    return HttpResponse.json(mockedAccountHolderEvent, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/test', () => {
    return HttpResponse.json(mockedNgnVbaNumberDetails, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/test/transactions', () => {
    return HttpResponse.json(mockedVbaNumberTransactions, { status: 200 });
  }),
  http.post('/admin/virtual-bank-account/manage', () => {
    return HttpResponse.json(
      {
        status: true,
        message: 'Virtual bank account manage action successful',
        data: {
          reference: 'KPY-AH-uCYNv4zpVJsQnha'
        }
      },
      { status: 200 }
    );
  }),

  http.post('/admin/virtual-bank-account/account-holders/manage', () => {
    return HttpResponse.json(
      {
        status: true,
        message: 'Virtual bank account holder manage action successful',
        data: {
          reference: 'KPY-AH-uCYNv4zpVJsQnha'
        }
      },
      { status: 200 }
    );
  }),

  http.post('/admin/virtual-bank-account/upgrade', () => {
    return HttpResponse.json({ status: true }, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/account-holders/test/events', () => {
    return HttpResponse.json(mockedAccountHolderEvent, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/account-holders/KPY-AH-rTMBm0r9fFd6iqb/kyc-history', () => {
    return HttpResponse.json(mockedAccountHolderKycHistory, { status: 200 });
  }),
  http.get('/admin/transactions/payins', () => {
    return HttpResponse.json(mockedVbaPayins, { status: 200 });
  }),
  http.get('/admin/virtual-bank-account/account-holders', () => {
    return HttpResponse.json(mockedAllVbaHolders, { status: 200 });
  })
];

export const transactionLimitHandlers = [
  http.get('http://localhost/admin/settings/virtual-bank-account/limits', () => {
    return HttpResponse.json(mockedTransactionLimits, { status: 200 });
  }),
  http.patch('/admin/settings/virtual-bank-account/limits', () => {
    return HttpResponse.json(mockedTransactionLimits, { status: 200 });
  })
];
export const paymentPreference = [
  http.get('/admin/settings/1/bank-transfers', () => {
    return HttpResponse.json(mockedBankTrxPaymentPreference, { status: 200 });
  }),

  http.get('/admin/settings/bank-transfer-settings-requests', () => {
    return HttpResponse.json(mockedBankTrxPaymentPreferenceList, { status: 200 });
  }),

  http.patch('/admin/settings/1/process-bank-transfer-settings-request', () => {
    return HttpResponse.json({ message: 'preference updated' }, { status: 200 });
  })
];

export const partnerFundingHandlers = [
  http.get('/admin/partner-funding/3/account', () => {
    return HttpResponse.json(mockedPartnerAccountBalance, { status: 200 });
  }),
  http.get('/admin/partner-funding/4/account', () => {
    return HttpResponse.json({ data: mockedPartnersAccountBalances['7'] }, { status: 200 });
  }),
  http.get('/admin/partner-funding/5/account', () => {
    return HttpResponse.json({ data: mockedPartnersAccountBalances['5'] }, { status: 200 });
  }),
  http.get('/admin/partner-funding/6/account', () => {
    return HttpResponse.json({ data: mockedPartnersAccountBalances['6'] }, { status: 200 });
  }),
  http.get('/admin/partner-funding/account', () => {
    return HttpResponse.json(mockedPartnerBalanceList, { status: 200 });
  }),
  http.get('/admin/partner-funding/transactions', () => {
    return HttpResponse.json(mockedPartnerBalanceTransactionHistory, { status: 200 });
  }),
  http.post('/admin/partner-funding/initiate', () => {
    return HttpResponse.json({}, { status: 200 });
  })
];

export const settlementConversionsHandler = [
  http.get('/admin/settings/1/settlement/conversions', () => HttpResponse.json(mockedConversions, { status: 200 })),

  http.patch('/admin/settings/1/settlement/conversions', async () => {
    return HttpResponse.json({ data: [] }, { status: 200 });
  })
];

export const pausedPaymentHandlers = [
  http.get('/admin/transactions/paused/payouts', () => {
    return HttpResponse.json({}, { status: 200 });
  })
];
export const billingConfigHandlers = [
  http.get('/admin/plans', () => {
    return HttpResponse.json(mockedCardIssuancePlans, { status: 200 });
  }),
  http.get('/admin/settings/card-issuance/billing-cycle', () => {
    HttpResponse.json(mockedCardIssuanceBillingCycle, { status: 200 });
  }),
  http.post('/admin/merchants/card-issuance', () => {
    return HttpResponse.json({}, { status: 200 });
  })
];

export const processorQueryHandlers = [
  http.post('/admin/virtual-bank-account/processor/transactions/repush', () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { cleared, ...data } = mockedProcessorQuery.data[0];

    return HttpResponse.json({ data: [data] }, { status: 200 });
  }),

  http.get('/admin/virtual-bank-account/processor/transactions/query', () => {
    return HttpResponse.json(mockedProcessorQuery, { status: 200 });
  })
];

export const reconciliationHandlers = [
  http.get('/admin/settlement-reconciliations', () => {
    return HttpResponse.json(mockedReconciliationHistory, { status: 200 });
  }),
  http.post('https://api.koraapi.com/utilities/v1/uploads/initiate', () => {
    return HttpResponse.json(mockedInitiateSettlementUploadResponse, { status: 200 });
  }),
  http.put('https://test-bucket.s3.amazonaws.com/test-file.csv', () => {
    return HttpResponse.json({}, { status: 200 });
  }),
  http.post('/admin/settlement-reconciliations', () => {
    return HttpResponse.json(mockedSettelementReconciliationHistory, { status: 200 });
  }),
  http.get('/admin/settlement-reconciliations/processor-configs', () => {
    return HttpResponse.json(mockedSettlementProcessorConfig, { status: 200 });
  })
];
export const identityConfigHandlers = [
  http.get('http://localhost/admin/identities/merchants/default/settings', () => {
    return HttpResponse.json(mockedDefaultMerchant, { status: 200 });
  })
];

export const merchantStatisticsHandlers = [
  http.get('http://localhost/admin/merchants/statistics', () => {
    return HttpResponse.json(mockedMerchantStatistics, { status: 200 });
  })
];

export const poolAccountsHandlers = [
  http.get('/admin/pool-accounts/references', () => {
    return HttpResponse.json(mockedPoolAccounts, { status: 200 });
  }),

  http.get('/admin/pool-accounts/transactions', () => {
    return HttpResponse.json(mockedPoolAccountTransactions, { status: 200 });
  }),

  http.post('/admin/pool-accounts/transactions', () => {
    return HttpResponse.json(mockedPoolAccountTransactions, { status: 200 });
  }),

  http.get('/admin/pool-accounts/transactions/test-ref', () => {
    return HttpResponse.json(mockedPoolAccountTransactionDetails, { status: 200 });
  })
];

export const merchantRegistrationHandlers = [
  http.get('/admin/merchants/ubo-register', () => {
    return HttpResponse.json(mockedMerchantRegistration, { status: 200 });
  })
];
