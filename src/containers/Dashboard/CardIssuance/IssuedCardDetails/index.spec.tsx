import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import useStore from '+zustandStore';

import IssuedCardDetails from './index';

const MockedIssuedCardDetails = () => {
  return (
    <MockIndexWithRoute route="dashboard/card-issuance/issuedCards/:id" initialEntries={['dashboard/card-issuance/issuedCards/1']}>
      <IssuedCardDetails />
    </MockIndexWithRoute>
  );
};

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

beforeEach(() => {
  useStore.setState({
    profile: {
      email: '<EMAIL>'
    }
  });
});
describe('IssuedCardDetails', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({ 'card_issuance_card.update': true });
  test('Renders Issuing', () => {
    render(<MockedIssuedCardDetails />);
  });

  test('Issuing is accessible', async () => {
    const { container } = render(<MockedIssuedCardDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Check if the go back button is present', () => {
    const { getByRole } = render(<MockedIssuedCardDetails />);
    const backButton = getByRole('button', { name: /Go back/i });
    expect(backButton).toBeInTheDocument();
  });

  test('should render the summary section', async () => {
    render(<MockedIssuedCardDetails />);
    await waitFor(() => {
      const summarySectionElement = screen.getByText('Summary');
      expect(summarySectionElement).toBeInTheDocument();
    });
  });

  test('summary information is displayed correctly', async () => {
    const { getByText } = render(<MockedIssuedCardDetails />);
    await waitFor(() => {
      expect(getByText('Active')).toBeInTheDocument();
      expect(getByText('Chijioke Agu', { selector: 'p.value' })).toBeInTheDocument();
      expect(getByText('09 / 2026')).toBeInTheDocument();
      expect(getByText('4 Sep 2024 10:03 PM')).toBeInTheDocument();
      expect(getByText('Mastercard', { selector: 'p.description' })).toBeInTheDocument();
    });
  });

  test('should render the available balance', async () => {
    render(<MockedIssuedCardDetails />);
    await waitFor(() => {
      const balanceValueElement = screen.getByTestId('digit-balance');
      expect(balanceValueElement).toBeInTheDocument();
      expect(balanceValueElement).toHaveTextContent('0');
    });
  });
  test('should render the card thumbnail', async () => {
    render(<MockedIssuedCardDetails />);
    await waitFor(() => {
      const cardThumbnailElement = screen.getByAltText('avatar');
      expect(cardThumbnailElement).toBeInTheDocument();
    });
  });

  test('clicking a tab updates the active tab state', async () => {
    const { getByText, queryByText } = render(<MockedIssuedCardDetails />);
    const transactionsTableElement = screen.getByTestId('transactions');
    expect(transactionsTableElement).toBeInTheDocument();

    const balanceHistoryTab = getByText('Balance History');

    fireEvent.click(balanceHistoryTab);
    const balanceHistoryTableElement = await screen.findByTestId('balance_history');
    await waitFor(() => {
      expect(queryByText('Transaction ID')).not.toBeInTheDocument();
      expect(balanceHistoryTableElement).toBeInTheDocument();
    });
  });

  test('clicking the "Manage Card" button displays the dropdown menu', async () => {
    const { getByText } = render(<MockedIssuedCardDetails />);
    await waitFor(() => {
      const manageCardButton = getByText('Manage Card');
      fireEvent.click(manageCardButton);
      expect(getByText('Suspend Card')).toBeInTheDocument();
      expect(getByText('Terminate Card')).toBeInTheDocument();
    });
  });
});
