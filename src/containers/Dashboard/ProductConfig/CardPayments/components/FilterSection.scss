@import 'styles/kpy-custom/variables';

.enhanced-filter-section {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 0;

  .filter-row {
    display: flex;
    align-items: center;
    gap: 16px;

    .search-control {
      flex: 1;
      min-width: 300px;

      .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .search-icon {
          position: absolute;
          left: 16px;
          color: #a9afbc;
          font-size: 18px;
          z-index: 2;
        }

        .search-input {
          width: 100%;
          height: 49px;
          padding: 13px 18px 13px 50px;
          border: 3px solid #dde2ec;
          border-radius: 5px;
          font-family: 'Averta PE', sans-serif;
          font-size: 18px;
          color: #414f5f;
          background-color: #ffffff;
          transition: border-color 0.2s ease;

          &::placeholder {
            color: #a9afbc;
            font-size: 18px;
          }

          &:focus {
            outline: none;
            border-color: #2376f3;
          }
        }
      }
    }

    .filter-controls {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-shrink: 0;

      .filter-dropdown-wrapper {
        position: relative;
        min-width: 150px;

        .filter-dropdown {
          width: 100%;
          height: 49px;
          padding: 13px 40px 13px 18px;
          border: 3px solid #dde2ec;
          border-radius: 5px;
          font-family: 'Averta PE', sans-serif;
          font-size: 18px;
          color: #354253;
          background-color: #ffffff;
          appearance: none;
          cursor: pointer;
          transition: border-color 0.2s ease;

          &:focus {
            outline: none;
            border-color: #2376f3;
          }
        }

        .dropdown-icon {
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          color: #3e4b5b;
          font-size: 18px;
          pointer-events: none;
        }
      }

      .date-filter-wrapper {
        min-width: 120px;

        .date-input-wrapper {
          position: relative;
          display: flex;
          align-items: center;

          .date-icon {
            position: absolute;
            left: 16px;
            color: #aabdce;
            font-size: 16px;
            z-index: 2;
          }

          .date-input {
            width: 100%;
            height: 49px;
            padding: 13px 18px 13px 45px;
            border: 3px solid #dde2ec;
            border-radius: 5px;
            font-family: 'Averta PE', sans-serif;
            font-size: 18px;
            color: #354253;
            background-color: #ffffff;
            cursor: pointer;
            transition: border-color 0.2s ease;

            &::placeholder {
              color: #354253;
              font-size: 18px;
            }

            &:focus {
              outline: none;
              border-color: #2376f3;
            }

            &:read-only {
              cursor: pointer;
            }
          }
        }
      }

      .go-button {
        width: 60px;
        height: 50px;
        background-color: #3e4b5b;
        border: none;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s ease;

        .os-icon-arrow-right {
          color: #ffffff;
          font-size: 20px;
        }

        &:hover:not(:disabled) {
          background-color: #2a3441;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  // Mobile responsive adjustments
  @media (max-width: 768px) {
    padding: 15px;

    .filter-row {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .search-control {
        min-width: auto;
      }

      .filter-controls {
        flex-direction: column;
        gap: 12px;

        .filter-dropdown-wrapper,
        .date-filter-wrapper {
          min-width: auto;
          width: 100%;
        }

        .go-button {
          align-self: center;
          width: 80px;
        }
      }
    }
  }

  @media (max-width: 1200px) {
    .filter-row {
      flex-wrap: wrap;

      .search-control {
        flex: 1 1 300px;
        min-width: 250px;
      }

      .filter-controls {
        flex: 1 1 auto;
        justify-content: flex-end;
      }
    }
  }
}
